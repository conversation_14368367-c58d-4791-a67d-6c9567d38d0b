
/* ========= SCROLL ANIMATION =========
   - Navbar height shrinks toward 75%
   - Coyote icon scales down to 75%
   - Wordmark flies upward (translates negative Y)
*/
(() => {
    const NAV_MIN = 0.75;  // 75% height
    const ICON_MIN = 0.75;  // 75% scale
    const FLY_MAX = 120;   // px the wordmark travels upward
    const RANGE = 320;   // px of scroll to complete the transition

    let ticking = false;

    function update() {
        const y = window.scrollY || 0;
        const t = Math.max(0, Math.min(1, y / RANGE)); // clamp 0..1

        const navScale = 1 - (1 - NAV_MIN) * t;
        const iconScale = 1 - (1 - ICON_MIN) * t;
        const fly = -FLY_MAX * t;

        root.style.setProperty("--navScale", navScale.toFixed(3));
        root.style.setProperty("--iconScale", iconScale.toFixed(3));
        root.style.setProperty("--fly", `${fly.toFixed(1)}px`);

        ticking = false;
    }

    function onScroll() {
        if (!ticking) {
            requestAnimationFrame(update);
            ticking = true;
        }
    }

    // Respect reduced motion
    const prefersReduced = matchMedia("(prefers-reduced-motion: reduce)");
    if (!prefersReduced.matches) {
        update();
        addEventListener("scroll", onScroll, { passive: true });
    }
})();
