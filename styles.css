/* ======= Replace these with your CodePen Asset URLs ======= */
:root {
    --fox-url: url("/Users/<USER>/www/sola/sola-logo-fox.png");
    /* e.g. sola-logo-fox.png  */
    --text-url: url("/Users/<USER>/www/sola/sola-logo-text.png");
    /* e.g. sola-logo-text.png */
    --hero-url: url("/Users/<USER>/www/sola/trippy.jpg");
    /* e.g. trippy.jpg         */

    /* Animation vars (updated by JS) */
    --navScale: 1;
    /* shrinks toward 0.75 */
    --iconScale: 1;
    /* shrinks toward 0.75 */
    --fly: 0px;
    /* negative => moves wordmark up */
}

/* ======= Page / Stage ======= */
* {
    box-sizing: border-box;
}

html,
body {
    height: 100%;
}

body {
    margin: 0;
    background: #111315;
    /* dark gray background */
    color: #e9e9e7;
    font-family: system-ui, -apple-system, Se<PERSON><PERSON> UI, <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
}


/* Navbar (black) */
.nav {
    position: sticky;
    top: 0;
    z-index: 10;
    height: calc(92px * var(--navScale));
    transition: height 160ms linear;
    background: #000;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 clamp(16px, 3.2vw, 28px);
    border-bottom: 1px solid rgba(255, 255, 255, .06);
}

/* Brand group: coyote icon + text wordmark */
.brand {
    display: flex;
    align-items: center;
    gap: clamp(10px, 1.8vw, 16px);
}

/* We’ll use <img> tags so you can swap PNG/SVG easily */
.icon,
.wordmark {
    display: block;
    height: auto;
}

/* Icon (coyote) scales with scroll */
.icon {
    width: clamp(54px, 8vw, 82px);
    transform-origin: left center;
    transform: scale(var(--iconScale));
    transition: transform 160ms linear;
    content: var(--fox-url);
    /* for reference only */
}

/* Wordmark flies upward on scroll */
.wordmark {
    width: 130px;
    transform: translateY(var(--fly));
    will-change: transform;
    transition: transform 80ms linear;
    content: var(--text-url);
    /* for reference only */
}

/* Menu icon (right) */
.menu {
    width: 48px;
    height: 40px;
    border: 0;
    background: transparent;
    padding: 0;
    display: grid;
    place-content: center;
    cursor: default;
}

.menu span {
    width: 26px;
    height: 3px;
    border-radius: 2px;
    background: #f0a3b1;
    /* pink */
    display: block;
    margin: 3px 0;
}

/* ======= Hero ======= */
.hero {
    position: relative;
    height: clamp(46svh, 62svh, 70svh);
    background:
        linear-gradient(180deg, rgba(0, 0, 0, 0.0) 10%, rgba(0, 0, 0, 0.55) 100%),
        var(--hero-url) center / cover no-repeat;
}


/* Content to allow scrolling inside the mock site */
.content {
    padding: clamp(18px, 3vw, 28px);
    color: #d9d9d7;
    line-height: 1.6;
    font-size: 1rem;
    background: #0d0d0f;
}

.content p {
    max-width: 64ch;
}